import pytest

class TestStoryAPI:
    """需求相关API测试"""
    
    def test_get_story_info(self, api_client, test_data):
        """测试获取产品需求工时"""
        response = api_client.get_story_info(test_data["story_ids"])
        
        # 验证响应结构
        assert response is not None
        assert "rsCode" in response
        assert "msg" in response
        assert "body" in response
        assert response["rsCode"] == "00000000"
        
        # 验证返回的需求数据
        stories = response["body"]["stories"]
        assert isinstance(stories, list)
        
        # 验证需求数据结构
        if stories:
            story = stories[0]
            assert "empid" in story
            assert "openedBy" in story
            assert "estimate" in story
            assert "story" in story
    
    def test_get_story_end(self, api_client, test_data):
        """测试获取需求任务工时（已完成）"""
        response = api_client.get_stories_end_info(test_data["story_ids"])
        
        # 验证响应结构
        assert response is not None
        assert "rsCode" in response
        assert "msg" in response
        assert "body" in response
        assert response["rsCode"] == "00000000"
        
        # 验证返回的任务数据
        tasks = response["body"]["task"]
        assert isinstance(tasks, list)
    
    def test_check_story_exists(self, api_client, test_data):
        """测试查询需求号是否存在"""
        response = api_client.check_stories_exist(test_data["story_ids"])
        
        # 验证响应结构
        assert response is not None
        assert "rsCode" in response
        assert "msg" in response
        assert "body" in response
        assert response["rsCode"] == "00000000"
        
        # 验证返回的需求ID数据
        story_ids = response["body"]["data"]
        assert isinstance(story_ids, list)
        
        # 验证需求ID是否存在
        for story_id in test_data["story_ids"]:
            assert int(story_id) in story_ids
    
    def test_get_stories_by_project(self, api_client, test_data):
        """测试根据项目ID查询项目需求列表"""
        response = api_client.get_stories_by_project(test_data["project_id"])
        
        # 验证响应结构
        assert response is not None
        assert "rsCode" in response
        assert "msg" in response
        assert "body" in response
        assert response["rsCode"] == "00000000"
        
        # 验证返回的需求数据
        stories = response["body"]["data"]
        assert isinstance(stories, list)
    
    def test_get_story_by_id(self, api_client, test_data):
        """测试根据需求ID查询需求详情"""
        # 使用第一个需求ID进行测试
        story_id = int(test_data["story_ids"][0])
        response = api_client.get_story_by_id(story_id)
        
        # 验证响应结构
        assert response is not None
        assert "rsCode" in response
        assert "msg" in response
        assert "body" in response
        assert response["rsCode"] == "00000000"
        
        # 验证需求详情数据 - 检查多种可能的字段名
        body = response["body"]
        
        # 打印响应体结构，帮助调试
        print(f"响应体结构: {body.keys()}")
        
        # 尝试不同的字段名
        if "data" in body:
            story_detail = body["data"]
        elif "storydetail" in body:
            story_detail = body["storydetail"]
        elif "detail" in body:
            story_detail = body["detail"]
        else:
            # 如果找不到预期的字段，直接使用整个body
            story_detail = body
        
        # 验证story ID，如果story_detail是字典
        if isinstance(story_detail, dict):
            assert "id" in story_detail, f"响应中缺少ID字段: {story_detail}"
            assert story_detail["id"] == story_id, f"Story ID不匹配: 期望 {story_id}, 实际 {story_detail['id']}"
        elif isinstance(story_detail, list) and story_detail:
            # 如果是列表，检查第一个元素
            assert "id" in story_detail[0], f"响应中缺少ID字段: {story_detail[0]}"
            assert story_detail[0]["id"] == story_id, f"Story ID不匹配: 期望 {story_id}, 实际 {story_detail[0]['id']}"
        else:
            assert False, f"无法在响应中找到有效的需求详情数据: {body}"
