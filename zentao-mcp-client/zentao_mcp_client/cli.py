"""
命令行接口模块
"""

import click
from .config import ClientConfig


@click.group()
def main():
    """Zentao MCP Client - 轻量级代理客户端"""
    pass


@main.command()
def configure():
    """配置后端服务连接信息"""
    config = ClientConfig()
    
    click.echo("=== Zentao MCP Client 配置 ===")
    click.echo()
    
    # 检查是否已配置
    if config.is_configured():
        click.echo("客户端已配置:")
        click.echo(f"  后端URL: {config.get_backend_url()}")
        click.echo(f"  API Key: {config.get_config_info()['api_key_preview']}")
        click.echo()
        
        reconfigure = click.confirm("是否重新配置?")
        if not reconfigure:
            return
    
    # 显示当前配置
    current_info = config.get_config_info()
    click.echo("当前配置:")
    click.echo(f"  配置文件: {current_info['config_file']}")
    click.echo(f"  后端URL: {current_info['backend_url']}")
    click.echo(f"  API Key: {current_info['api_key_preview']}")
    click.echo()
    
    # 获取后端URL
    current_url = config.get_backend_url()
    default_url = current_url if current_url else "http://localhost:8000"
    
    backend_url = click.prompt(
        "请输入后端服务URL",
        default=default_url,
        type=str
    )
    
    # 确保URL格式正确
    if not backend_url.startswith(('http://', 'https://')):
        backend_url = f"http://{backend_url}"
    
    # 移除末尾的斜杠
    backend_url = backend_url.rstrip('/')
    
    # 获取API Key
    current_key = config.get_api_key()
    api_key = click.prompt(
        "请输入API Key",
        default=current_key if current_key else "",
        hide_input=True,
        type=str
    )
    
    # 保存配置
    config.set_backend_url(backend_url)
    config.set_api_key(api_key)
    
    click.echo()
    click.echo("配置完成")
    click.echo("配置保存成功")
    click.echo(f"后端URL: {backend_url}")
    click.echo(f"API Key: {api_key[:8]}..." if len(api_key) > 8 else "API Key: ***")


@main.command()
def info():
    """显示当前配置信息"""
    config = ClientConfig()
    info = config.get_config_info()
    
    click.echo("=== Zentao MCP Client 配置信息 ===")
    click.echo(f"配置文件: {info['config_file']}")
    click.echo(f"后端URL: {info['backend_url']}")
    if 'backend_url_source' in info:
        click.echo(f"后端URL来源: {info['backend_url_source']} (env/home/<USER>")
    click.echo(f"API Key: {info['api_key_preview']}")
    if 'api_key_source' in info:
        click.echo(f"API Key来源: {info['api_key_source']} (env/home/<USER>")
    if config.is_configured():
        click.echo("配置状态: ✅ 已配置")
    else:
        click.echo("配置状态: ❌ 未配置")


@main.command()
@click.option('--host', default='localhost', help='监听主机地址 (仅HTTP模式)')
@click.option('--port', default=8080, help='监听端口 (仅HTTP模式)')
@click.option('--mode', default='stdio', type=click.Choice(['stdio', 'http', 'sse']), help='运行模式')
def start(host: str, port: int, mode: str):
    """启动Zentao MCP客户端代理服务"""
    config = ClientConfig()

    if not config.is_configured():
        click.echo("❌ 客户端未配置，请先运行 'zentao-mcp-client configure'")
        return

    click.echo("🚀 启动Zentao MCP客户端代理服务...")
    click.echo(f"运行模式: {mode.upper()}")
    if mode == "http":
        click.echo(f"监听地址: http://{host}:{port}")
    click.echo(f"后端服务: {config.get_backend_url()}")
    click.echo()

    # 导入并启动代理服务
    from .proxy import start_proxy_server
    start_proxy_server(host, port, config, mode)


if __name__ == '__main__':
    main()