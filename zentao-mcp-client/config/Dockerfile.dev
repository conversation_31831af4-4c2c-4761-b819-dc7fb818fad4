# 开发环境Dockerfile
# 使用ARG支持包管理器镜像源配置
ARG PIP_INDEX_URL=https://pypi.tuna.tsinghua.edu.cn/simple
ARG PIP_TRUSTED_HOST=pypi.tuna.tsinghua.edu.cn

FROM python:3.11-slim

WORKDIR /app

# 配置时区和语言环境
ENV TZ=Asia/Shanghai \
    LANG=zh_CN.UTF-8 \
    LC_ALL=zh_CN.UTF-8

# 配置pip镜像源
ENV PIP_INDEX_URL=${PIP_INDEX_URL} \
    PIP_TRUSTED_HOST=${PIP_TRUSTED_HOST}

# 安装系统依赖
RUN apt-get update && apt-get install -y \
    curl \
    locales \
    && echo "zh_CN.UTF-8 UTF-8" > /etc/locale.gen \
    && locale-gen \
    && rm -rf /var/lib/apt/lists/*

# 复制依赖文件
COPY pyproject.toml uv.lock ./

# 安装uv和Python依赖
RUN if [ -n "${PIP_INDEX_URL}" ] && [ -n "${PIP_TRUSTED_HOST}" ]; then \
        pip install -i ${PIP_INDEX_URL} --trusted-host ${PIP_TRUSTED_HOST} uv; \
    else \
        pip install uv; \
    fi && \
    uv sync --dev

# 复制应用代码
COPY . .

# 创建必要目录
RUN mkdir -p logs

# 暴露端口（HTTP模式）
EXPOSE 8080

# 健康检查
HEALTHCHECK --interval=30s --timeout=10s --start-period=10s --retries=3 \
    CMD curl -f http://localhost:8080/health || exit 1

# 启动命令（默认HTTP模式）
CMD ["uv", "run", "python", "-m", "zentao_mcp_client", "start", "--mode", "http", "--host", "0.0.0.0", "--port", "8080"]
