version: '3.8'

services:
  client:
    build:
      context: ../
      dockerfile: config/Dockerfile.dev
    container_name: ${CONTAINER_NAME:-zentao-client-test}
    environment:
      - ZENTAO_MCP_BACKEND_URL=${ZENTAO_MCP_BACKEND_URL:-http://localhost:8000}
      - ZENTAO_MCP_API_KEY=${ZENTAO_MCP_API_KEY:-test-api-key-please-change}
      - CLIENT_MODE=${CLIENT_MODE:-http}
      - CLIENT_HOST=${CLIENT_HOST:-0.0.0.0}
      - CLIENT_PORT=${CLIENT_PORT:-8080}
      - LOG_LEVEL=${LOG_LEVEL:-INFO}
      - ENVIRONMENT=testing
    ports:
      - "${CLIENT_PORT:-8080}:8080"
    volumes:
      - client_test_logs:/app/logs
    networks:
      - zentao-test
    restart: unless-stopped
    deploy:
      resources:
        limits:
          memory: 256M
          cpus: '0.25'
        reservations:
          memory: 128M
          cpus: '0.1'
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8080/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 30s
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"
    profiles:
      - client

networks:
  zentao-test:
    driver: bridge
    name: ${NETWORK_NAME:-zentao-test-network}

volumes:
  client_test_logs:
    driver: local
