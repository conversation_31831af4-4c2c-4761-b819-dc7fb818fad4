# 生产环境Dockerfile - 多阶段构建
# 使用ARG支持包管理器镜像源配置
ARG PIP_INDEX_URL=https://pypi.tuna.tsinghua.edu.cn/simple
ARG PIP_TRUSTED_HOST=pypi.tuna.tsinghua.edu.cn
ARG APT_MIRROR=mirrors.tuna.tsinghua.edu.cn

FROM python:3.11-slim AS builder

# 重新声明ARG变量（FROM之后需要重新声明）
ARG PIP_INDEX_URL
ARG PIP_TRUSTED_HOST
ARG APT_MIRROR

WORKDIR /app

# 配置环境变量
ENV DEBIAN_FRONTEND=noninteractive \
    PIP_INDEX_URL=${PIP_INDEX_URL} \
    PIP_TRUSTED_HOST=${PIP_TRUSTED_HOST} \
    APT_MIRROR=${APT_MIRROR}

# 配置APT镜像源并安装构建依赖
RUN set -eux; \
    # 完全替换APT源配置
    rm -rf /etc/apt/sources.list.d/*; \
    mkdir -p /etc/apt; \
    # 配置清华大学镜像源
    echo "deb https://${APT_MIRROR}/debian/ trixie main" > /etc/apt/sources.list; \
    echo "deb https://${APT_MIRROR}/debian/ trixie-updates main" >> /etc/apt/sources.list; \
    echo "deb https://${APT_MIRROR}/debian-security/ trixie-security main" >> /etc/apt/sources.list; \
    # 更新包列表并安装构建依赖
    apt-get update; \
    apt-get install -y --no-install-recommends \
        build-essential \
        ca-certificates; \
    # 清理APT缓存
    apt-get clean; \
    rm -rf /var/lib/apt/lists/* /tmp/* /var/tmp/*;

# 复制依赖文件
COPY pyproject.toml uv.lock ./

# 安装uv和Python依赖（使用国内镜像源）
RUN set -eux; \
    # 安装uv包管理器
    pip install --no-cache-dir -i ${PIP_INDEX_URL} --trusted-host ${PIP_TRUSTED_HOST} uv; \
    # 同步依赖（生产环境，使用国内镜像源）
    UV_INDEX_URL=${PIP_INDEX_URL} uv sync --no-dev --no-cache; \
    # 清理pip缓存
    pip cache purge || true; \
    # 清理uv缓存
    uv cache clean || true;

# 复制应用代码
COPY . .

# 构建可执行文件
RUN set -eux; \
    # 安装PyInstaller
    pip install --no-cache-dir -i ${PIP_INDEX_URL} --trusted-host ${PIP_TRUSTED_HOST} pyinstaller; \
    # 构建可执行文件
    python build_executable.py; \
    # 清理pip缓存
    pip cache purge || true;

# 生产阶段
FROM python:3.11-slim

# 重新声明ARG变量（FROM之后需要重新声明）
ARG APT_MIRROR

WORKDIR /app

# 配置环境变量
ENV DEBIAN_FRONTEND=noninteractive \
    APT_MIRROR=${APT_MIRROR}

# 配置APT镜像源并安装运行时依赖
RUN set -eux; \
    # 完全替换APT源配置
    rm -rf /etc/apt/sources.list.d/*; \
    mkdir -p /etc/apt; \
    # 配置清华大学镜像源
    echo "deb https://${APT_MIRROR}/debian/ trixie main" > /etc/apt/sources.list; \
    echo "deb https://${APT_MIRROR}/debian/ trixie-updates main" >> /etc/apt/sources.list; \
    echo "deb https://${APT_MIRROR}/debian-security/ trixie-security main" >> /etc/apt/sources.list; \
    # 更新包列表并安装运行时依赖
    apt-get update; \
    apt-get install -y --no-install-recommends \
        curl \
        ca-certificates; \
    # 创建用户
    groupadd -r zentao && useradd -r -g zentao zentao; \
    # 清理APT缓存
    apt-get clean; \
    rm -rf /var/lib/apt/lists/* /tmp/* /var/tmp/*;

# 复制可执行文件
COPY --from=builder /app/dist/zentao-mcp-client /usr/local/bin/zentao-mcp-client
RUN chmod +x /usr/local/bin/zentao-mcp-client

# 创建必要目录
RUN mkdir -p logs && chown -R zentao:zentao logs

# 切换到非root用户
USER zentao

# 暴露端口
EXPOSE 8080

# 健康检查
HEALTHCHECK --interval=30s --timeout=10s --start-period=10s --retries=3 \
    CMD curl -f http://localhost:8080/health || exit 1

# 启动命令
CMD ["zentao-mcp-client", "start", "--mode", "http", "--host", "0.0.0.0", "--port", "8080"]
