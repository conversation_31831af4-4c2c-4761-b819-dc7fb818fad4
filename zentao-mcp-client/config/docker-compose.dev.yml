version: '3.8'

services:
  client:
    build:
      context: ../
      dockerfile: config/Dockerfile.dev
    container_name: ${CONTAINER_NAME:-zentao-client-dev}
    environment:
      - ZENTAO_MCP_BACKEND_URL=${ZENTAO_MCP_BACKEND_URL:-http://zentao-backend-dev:8000}
      - ZENTAO_MCP_API_KEY=${ZENTAO_MCP_API_KEY:-dev-api-key-for-testing}
      - CLIENT_MODE=${CLIENT_MODE:-http}
      - CLIENT_HOST=${CLIENT_HOST:-0.0.0.0}
      - CLIENT_PORT=${CLIENT_PORT:-8080}
      - LOG_LEVEL=${LOG_LEVEL:-DEBUG}
      - ENVIRONMENT=development
    ports:
      - "${CLIENT_PORT:-8080}:8080"
    volumes:
      - client_dev_logs:/app/logs
    networks:
      - zentao-dev
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8080/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 30s
    depends_on:
      - backend
    profiles:
      - client

  # 后端服务引用（用于依赖）
  backend:
    image: zentao-backend-dev:latest
    container_name: zentao-backend-dev
    networks:
      - zentao-dev
    external: true

networks:
  zentao-dev:
    driver: bridge
    name: ${NETWORK_NAME:-zentao-dev-network}
    external: true

volumes:
  client_dev_logs:
    driver: local
