version: '3.8'

services:
  client:
    build:
      context: ../
      dockerfile: config/Dockerfile.prod
    container_name: ${CONTAINER_NAME:-zentao-client-prod}
    environment:
      - ZENTAO_MCP_BACKEND_URL=${ZENTAO_MCP_BACKEND_URL}
      - ZENTAO_MCP_API_KEY=${ZENTAO_MCP_API_KEY}
      - CLIENT_MODE=${CLIENT_MODE:-http}
      - CLIENT_HOST=${CLIENT_HOST:-0.0.0.0}
      - CLIENT_PORT=${CLIENT_PORT:-8080}
      - LOG_LEVEL=${LOG_LEVEL:-INFO}
      - ENVIRONMENT=production
    ports:
      - "${CLIENT_PORT:-8080}:8080"
    volumes:
      - client_prod_logs:/app/logs
    networks:
      - zentao-prod
    restart: unless-stopped
    deploy:
      replicas: ${CLIENT_REPLICAS:-1}
      resources:
        limits:
          memory: 256M
          cpus: '0.25'
        reservations:
          memory: 128M
          cpus: '0.1'
      restart_policy:
        condition: on-failure
        delay: 5s
        max_attempts: 3
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8080/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 30s
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"
    depends_on:
      - backend
    profiles:
      - client

  # 后端服务引用（用于依赖）
  backend:
    image: zentao-backend-prod:latest
    container_name: zentao-backend-prod
    networks:
      - zentao-prod
    external: true

networks:
  zentao-prod:
    driver: bridge
    name: ${NETWORK_NAME:-zentao-prod-network}
    external: true

volumes:
  client_prod_logs:
    driver: local
