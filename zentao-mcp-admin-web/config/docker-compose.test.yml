version: '3.8'

services:
  frontend:
    build:
      context: ../
      dockerfile: config/Dockerfile.test
    container_name: ${CONTAINER_NAME:-zentao-frontend-test}
    environment:
      - NODE_ENV=testing
      - VITE_API_BASE_URL=${VITE_API_BASE_URL:-http://zentao-backend-test:8000}
      - VITE_API_VERSION=${VITE_API_VERSION:-v1}
      - VITE_API_TIMEOUT=${VITE_API_TIMEOUT:-15000}
      - VITE_APP_ENV=testing
      - VITE_ENABLE_MOCK=${VITE_ENABLE_MOCK:-false}
      - VITE_ENABLE_DEVTOOLS=${VITE_ENABLE_DEVTOOLS:-false}
      - VITE_LOG_LEVEL=${VITE_LOG_LEVEL:-info}
      - VITE_ENABLE_CONSOLE=${VITE_ENABLE_CONSOLE:-false}
      - VITE_BUILD_SOURCEMAP=${VITE_BUILD_SOURCEMAP:-true}
      - VITE_BUILD_MINIFY=${VITE_BUILD_MINIFY:-true}
    ports:
      - "${FRONTEND_PORT:-3000}:8080"
    networks:
      - zentao-test
    restart: unless-stopped
    deploy:
      resources:
        limits:
          memory: 256M
          cpus: '0.25'
        reservations:
          memory: 128M
          cpus: '0.1'
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8080/"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 30s
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"

networks:
  zentao-test:
    driver: bridge
    name: ${NETWORK_NAME:-zentao-test-network}
    external: true
