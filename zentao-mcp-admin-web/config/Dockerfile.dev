# 开发环境Dockerfile
# 使用ARG支持包管理器镜像源配置
ARG NPM_REGISTRY=https://registry.npmmirror.com
ARG APK_MIRROR=mirrors.aliyun.com

FROM node:18-alpine

# 重新声明ARG变量（FROM之后需要重新声明）
ARG NPM_REGISTRY
ARG APK_MIRROR

WORKDIR /app

# 配置时区和语言环境
ENV TZ=Asia/Shanghai

# 配置Alpine镜像源
RUN sed -i "s/dl-cdn.alpinelinux.org/${APK_MIRROR}/g" /etc/apk/repositories

# 安装系统依赖
RUN apk add --no-cache curl tzdata

# 配置npm镜像源
RUN npm config set registry ${NPM_REGISTRY}

# 复制package文件
COPY package*.json ./
COPY bun.lock* ./

# 安装依赖（ARM64架构跳过bun）
RUN if [ -f "bun.lock" ] && [ "$(uname -m)" != "aarch64" ]; then \
        npm install -g bun && bun install; \
    else \
        npm install; \
    fi

# 复制源代码
COPY . .

# 暴露端口
EXPOSE 5173

# 健康检查
HEALTHCHECK --interval=30s --timeout=10s --start-period=30s --retries=3 \
    CMD curl -f http://localhost:5173/ || exit 1

# 启动开发服务器
CMD if [ -f "bun.lock" ] && [ "$(uname -m)" != "aarch64" ]; then \
        bun run dev --host 0.0.0.0; \
    else \
        npm run dev -- --host 0.0.0.0; \
    fi
