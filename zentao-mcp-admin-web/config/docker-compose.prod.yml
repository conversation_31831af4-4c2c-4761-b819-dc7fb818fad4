version: '3.8'

services:
  frontend:
    build:
      context: ../
      dockerfile: config/Dockerfile.prod
    container_name: ${CONTAINER_NAME:-zentao-frontend-prod}
    environment:
      - NODE_ENV=production
      - VITE_API_BASE_URL=${VITE_API_BASE_URL:-https://api.yourdomain.com}
      - VITE_API_VERSION=${VITE_API_VERSION:-v1}
      - VITE_API_TIMEOUT=${VITE_API_TIMEOUT:-20000}
      - VITE_APP_ENV=production
      - VITE_ENABLE_MOCK=${VITE_ENABLE_MOCK:-false}
      - VITE_ENABLE_DEVTOOLS=${VITE_ENABLE_DEVTOOLS:-false}
      - VITE_LOG_LEVEL=${VITE_LOG_LEVEL:-error}
      - VITE_ENABLE_CONSOLE=${VITE_ENABLE_CONSOLE:-false}
      - VITE_ENABLE_PWA=${VITE_ENABLE_PWA:-true}
      - VITE_BUILD_SOURCEMAP=${VITE_BUILD_SOURCEMAP:-false}
      - VITE_BUILD_MINIFY=${VITE_BUILD_MINIFY:-true}
      - VITE_BUILD_GZIP=${VITE_BUILD_GZIP:-true}
    ports:
      - "${FRONTEND_HTTP_PORT:-80}:8080"
      - "${FRONTEND_HTTPS_PORT:-443}:8443"
    networks:
      - zentao-prod
    restart: unless-stopped
    deploy:
      replicas: ${FRONTEND_REPLICAS:-2}
      resources:
        limits:
          memory: 256M
          cpus: '0.25'
        reservations:
          memory: 128M
          cpus: '0.1'
      restart_policy:
        condition: on-failure
        delay: 5s
        max_attempts: 3
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8080/"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 30s
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"

networks:
  zentao-prod:
    driver: bridge
    name: ${NETWORK_NAME:-zentao-prod-network}
    external: true
