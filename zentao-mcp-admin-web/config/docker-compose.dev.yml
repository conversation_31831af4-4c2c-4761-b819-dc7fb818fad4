version: '3.8'

services:
  frontend:
    build:
      context: ../
      dockerfile: config/Dockerfile.dev
    container_name: ${CONTAINER_NAME:-zentao-frontend-dev}
    environment:
      - NODE_ENV=development
      - VITE_API_BASE_URL=${VITE_API_BASE_URL:-http://localhost:8000}
      - VITE_API_VERSION=${VITE_API_VERSION:-v1}
      - VITE_API_TIMEOUT=${VITE_API_TIMEOUT:-10000}
      - VITE_APP_ENV=development
      - VITE_ENABLE_MOCK=${VITE_ENABLE_MOCK:-false}
      - VITE_ENABLE_DEVTOOLS=${VITE_ENABLE_DEVTOOLS:-true}
      - VITE_LOG_LEVEL=${VITE_LOG_LEVEL:-debug}
      - VITE_ENABLE_CONSOLE=${VITE_ENABLE_CONSOLE:-true}
    ports:
      - "${FRONTEND_PORT:-5173}:5173"
    volumes:
      - ../src:/app/src:ro
      - ../public:/app/public:ro
      - ../index.html:/app/index.html:ro
      - ../vite.config.ts:/app/vite.config.ts:ro
      - ../tsconfig.json:/app/tsconfig.json:ro
      - ../tailwind.config.js:/app/tailwind.config.js:ro
      - ../postcss.config.js:/app/postcss.config.js:ro
    networks:
      - zentao-dev
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:5173/"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 30s

networks:
  zentao-dev:
    driver: bridge
    name: ${NETWORK_NAME:-zentao-dev-network}
    external: true
