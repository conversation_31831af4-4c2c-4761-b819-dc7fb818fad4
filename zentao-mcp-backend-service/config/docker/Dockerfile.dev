# 使用ARG支持包管理器镜像源配置
ARG PIP_INDEX_URL=https://pypi.tuna.tsinghua.edu.cn/simple
ARG PIP_TRUSTED_HOST=pypi.tuna.tsinghua.edu.cn
ARG APT_MIRROR=mirrors.tuna.tsinghua.edu.cn

FROM python:3.11-slim

# 重新声明ARG变量（FROM之后需要重新声明）
ARG PIP_INDEX_URL
ARG PIP_TRUSTED_HOST
ARG APT_MIRROR

WORKDIR /app

# 配置时区和语言环境
ENV TZ=Asia/Shanghai \
    LANG=zh_CN.UTF-8 \
    LC_ALL=zh_CN.UTF-8 \
    DEBIAN_FRONTEND=noninteractive

# 配置pip镜像源和APT镜像源
ENV PIP_INDEX_URL=${PIP_INDEX_URL} \
    PIP_TRUSTED_HOST=${PIP_TRUSTED_HOST} \
    APT_MIRROR=${APT_MIRROR}

# 配置APT镜像源并安装系统依赖（多阶段优化）
RUN set -eux; \
    # 完全替换APT源配置
    rm -rf /etc/apt/sources.list.d/*; \
    mkdir -p /etc/apt; \
    # 配置清华大学镜像源（完全替换）
    echo "deb https://${APT_MIRROR}/debian/ trixie main" > /etc/apt/sources.list; \
    echo "deb https://${APT_MIRROR}/debian/ trixie-updates main" >> /etc/apt/sources.list; \
    echo "deb https://${APT_MIRROR}/debian-security/ trixie-security main" >> /etc/apt/sources.list; \
    # 清理可能存在的其他源文件
    find /etc/apt -name "*.list" -not -name "sources.list" -delete || true; \
    # 更新包列表并安装依赖
    apt-get update; \
    apt-get install -y --no-install-recommends \
        curl \
        sqlite3 \
        locales \
        ca-certificates; \
    # 配置语言环境
    echo "zh_CN.UTF-8 UTF-8" > /etc/locale.gen; \
    locale-gen; \
    # 清理APT缓存
    apt-get clean; \
    rm -rf /var/lib/apt/lists/* /tmp/* /var/tmp/*;

# 第一阶段：安装Python依赖（利用Docker层缓存）
COPY pyproject.toml uv.lock ./

# 安装uv和Python依赖（优化缓存策略）
RUN set -eux; \
    # 安装uv包管理器
    pip install --no-cache-dir -i ${PIP_INDEX_URL} --trusted-host ${PIP_TRUSTED_HOST} uv; \
    # 同步依赖（开发环境，使用国内镜像源）
    UV_INDEX_URL=${PIP_INDEX_URL} uv sync --dev --no-cache; \
    # 清理pip缓存
    pip cache purge || true; \
    # 清理uv缓存
    uv cache clean || true;

# 第二阶段：复制应用代码（分离代码和依赖层）
COPY . .

# 创建必要目录并设置权限
RUN set -eux; \
    mkdir -p data logs; \
    chmod 755 data logs;

# 健康检查
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:8000/health || exit 1

# 设置Python路径并启动命令
ENV PYTHONPATH=/app
CMD ["uv", "run", "uvicorn", "main:app", "--host", "0.0.0.0", "--port", "8000", "--reload"]
