version: '3.8'

services:
  backend:
    build:
      context: ../../../zentao-mcp-backend-service
      dockerfile: config/docker/Dockerfile.test
    container_name: ${CONTAINER_NAME:-zentao-backend-test}
    env_file:
      - ../environments/test.env
    ports:
      - "8000:8000"
    volumes:
      - zentao-backend-test-logs:/app/logs
      - zentao-backend-test-data:/app/data
    networks:
      - zentao-test
    restart: unless-stopped
    deploy:
      resources:
        limits:
          memory: 512M
          cpus: '0.25'
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 30s

networks:
  zentao-test:
    driver: bridge
    name: ${NETWORK_NAME:-zentao-test-network}

volumes:
  zentao-backend-test-logs:
    driver: local
  zentao-backend-test-data:
    driver: local
