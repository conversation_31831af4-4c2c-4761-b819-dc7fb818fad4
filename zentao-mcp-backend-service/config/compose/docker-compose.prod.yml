version: '3.8'

services:
  backend:
    build:
      context: ../../../zentao-mcp-backend-service
      dockerfile: config/docker/Dockerfile.prod
    container_name: ${CONTAINER_NAME:-zentao-backend-prod}
    env_file:
      - ../environments/prod.env
    ports:
      - "8000:8000"
    volumes:
      - zentao-backend-prod-logs:/app/logs
      - zentao-backend-prod-data:/app/data
    networks:
      - zentao-prod
    restart: unless-stopped
    deploy:
      replicas: ${REPLICAS:-2}
      resources:
        limits:
          memory: 1G
          cpus: '0.5'
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s

networks:
  zentao-prod:
    driver: bridge
    name: ${NETWORK_NAME:-zentao-prod-network}

volumes:
  zentao-backend-prod-logs:
    driver: local
  zentao-backend-prod-data:
    driver: local
