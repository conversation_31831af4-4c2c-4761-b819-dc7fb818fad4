version: '3.8'

services:
  backend:
    build:
      context: ../../../zentao-mcp-backend-service
      dockerfile: config/docker/Dockerfile.dev
    container_name: ${CONTAINER_NAME:-zentao-backend-dev}
    env_file:
      - ../environments/dev.env
    ports:
      - "8000:8000"
    volumes:
      - zentao-backend-dev-data:/app/data
      - zentao-backend-dev-logs:/app/logs
    networks:
      - zentao-dev
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

networks:
  zentao-dev:
    driver: bridge
    name: ${NETWORK_NAME:-zentao-dev-network}

volumes:
  zentao-backend-dev-data:
    driver: local
  zentao-backend-dev-logs:
    driver: local
